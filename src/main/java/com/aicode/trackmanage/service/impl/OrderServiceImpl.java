package com.aicode.trackmanage.service.impl;

import com.aicode.trackmanage.entity.Order;
import com.aicode.trackmanage.service.OrderService;
import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.aigc.generation.models.QwenParam;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.MessageManager;
import com.alibaba.dashscope.common.Role;

import java.io.IOException;
import java.util.Map;

import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.aicode.trackmanage.mapper.OrderMapper;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class OrderServiceImpl implements OrderService {
    private static final Logger log = LoggerFactory.getLogger(OrderServiceImpl.class);

    @Value("${aliyun.dashscope.api-key}")
    private String dashscopeApiKey;

    @Autowired
    private OrderMapper orderMapper;

    @Override
    public List<Order> findAll() {
        // TODO: 实现数据库查询
        return new ArrayList<>();
    }

    @Override
    public Order findById(Long id) {
        // TODO: 实现���������据库查询
        return null;
    }

    @Override
    public List<Map<String, Object>> calculateFreight(Map<String, Object> orderMap) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        String supplier = (String) orderMap.get("supplier");
        String origin = (String) orderMap.get("origin");
        String destination = (String) orderMap.get("destination");
        Double weight = orderMap.get("weight") != null ? Double.valueOf(orderMap.get("weight").toString()) : 0.0;
        Double volume = orderMap.get("volume") != null ? Double.valueOf(orderMap.get("volume").toString()) : 0.0;
        Boolean needTransit = orderMap.get("needTransit") != null ? Boolean.valueOf(orderMap.get("needTransit").toString()) : false;

        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<Order> wrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
        wrapper.eq("carrier", supplier);
        List<Order> dbOrders = orderMapper.selectList(wrapper);
        if (dbOrders == null || dbOrders.isEmpty()) {
            return resultList;
        }
        try {
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            for (Order dbOrder : dbOrders) {
                if (dbOrder.getLlmJson() == null) continue;
                com.fasterxml.jackson.databind.JsonNode root = objectMapper.readTree(dbOrder.getLlmJson());
                com.fasterxml.jackson.databind.JsonNode routes = root.path("routes");
                if (!routes.isArray()) continue;

                for (com.fasterxml.jackson.databind.JsonNode route : routes) {
                    if (!route.path("origin").asText().equalsIgnoreCase(origin) ||
                            !route.path("destination").asText().equalsIgnoreCase(destination)) {
                        continue;
                    }
                    double chargeWeight = weight;
                    double unitPrice = 0.0;
                    double baseCost = 0.0;
                    double extraFee = 0.0;
                    double totalCost = 0.0;
                    String quoteNo = dbOrder.getQuotationNo() != null ? dbOrder.getQuotationNo() : "";

                    // 计算密度
                    double density = (volume > 0) ? weight / volume : 0;
                    com.fasterxml.jackson.databind.JsonNode rates = route.path("rates");

                    // 1. 找到所有满足重量区间的rate
                    List<com.fasterxml.jackson.databind.JsonNode> candidateRates = new ArrayList<>();
                    for (com.fasterxml.jackson.databind.JsonNode rate : rates) {
                        double minWt = rate.path("min_wt").asDouble();
                        double maxWt = rate.path("max_wt").asDouble();
                        if (weight >= minWt && weight <= maxWt) {
                            candidateRates.add(rate);
                        }
                    }
                    // 2. 在候选rate中找到密度最接近且大于等于当前密度的最小density_factor
                    com.fasterxml.jackson.databind.JsonNode bestRate = null;
                    double minDensityDiff = Double.MAX_VALUE;
                    for (com.fasterxml.jackson.databind.JsonNode rate : candidateRates) {
                        double densityFactor = rate.path("density_factor").asDouble(0);
                        if (densityFactor == 0) densityFactor = Double.MAX_VALUE; // 0视为无限大
                        if (density <= densityFactor) {
                            double diff = densityFactor - density;
                            if (diff < minDensityDiff) {
                                minDensityDiff = diff;
                                bestRate = rate;
                            }
                        }
                    }
                    // 3. 如果没有密度大于等于当前密度的rate，则取所有候选rate中density_factor最接近的
                    if (bestRate == null && !candidateRates.isEmpty()) {
                        double minAbsDiff = Double.MAX_VALUE;
                        for (com.fasterxml.jackson.databind.JsonNode rate : candidateRates) {
                            double densityFactor = rate.path("density_factor").asDouble(0);
                            double diff = Math.abs(density - densityFactor);
                            if (diff < minAbsDiff) {
                                minAbsDiff = diff;
                                bestRate = rate;
                            }
                        }
                    }
                    // 4. 赋值
                    if (bestRate != null) {
                        unitPrice = bestRate.path("price").asDouble();
                        baseCost = chargeWeight * unitPrice;
                    }

                    // 附加费
                    com.fasterxml.jackson.databind.JsonNode surcharges = route.path("surcharges");
                    if (surcharges.isArray()) {
                        for (com.fasterxml.jackson.databind.JsonNode surcharge : surcharges) {
                            try {
                                double fee = Double.parseDouble(surcharge.path("surcharges_cost").asText("0"));
                                extraFee += fee;
                            } catch (Exception ignore) {
                            }
                        }
                    }
                    // 中转费用
                    if (needTransit) {
                        com.fasterxml.jackson.databind.JsonNode transfers = route.path("transfers");
                        if (transfers.isArray() && transfers.size() > 0) {
                            for (com.fasterxml.jackson.databind.JsonNode transfer : transfers) {
                                extraFee += transfer.path("transfer_cost").asDouble(0.0);
                            }
                        }
                    }
                    totalCost = baseCost + extraFee;

                    Map<String, Object> row = new java.util.HashMap<>();
                    row.put("supplier", supplier);
                    row.put("origin", origin);
                    row.put("destination", destination);
                    row.put("chargeWeight", chargeWeight);
                    row.put("unitPrice", unitPrice);
                    row.put("baseCost", baseCost);
                    row.put("extraFee", extraFee);
                    row.put("totalCost", totalCost);
                    row.put("quoteNo", quoteNo);
                    row.put("densityFactor", bestRate != null ? bestRate.path("density_factor").asDouble() : null);
                    resultList.add(row);
                }
            }
            return resultList;
        } catch (Exception e) {
            e.printStackTrace();
            return resultList;
        }
    }

    @Override
    public void importFromPdf(MultipartFile file) {
        try {
            PDDocument document = PDDocument.load(file.getInputStream());
            PDFTextStripper stripper = new PDFTextStripper();
            String text = stripper.getText(document);

            Message systemMsg = Message.builder()
                    .role(Role.SYSTEM.getValue())
                    .content("你是一个专业的物流数据信息提取助手，请提取关键信息并以JSON格式返回。")
                    .build();

            Message userMsg = Message.builder()
                    .role(Role.USER.getValue())
                    .content("从以下运单文本中提取订单信息（包括：订单号、客户名称、起点、终点、重量、体积等）：\n" + text)
                    .build();

            QwenParam param = QwenParam.builder()
                    .model(Generation.Models.QWEN_TURBO)
                    .apiKey(dashscopeApiKey)
                    .messages(List.of(systemMsg, userMsg))
                    .resultFormat("message")
                    .topP(0.8)
                    .temperature(0.7f)
                    .maxTokens(2000)
                    .build();

            Generation generation = new Generation();
            GenerationResult result = generation.call(param);
            String parsedInfo = result.getOutput().getText();

            // TODO: 解析parsedInfo并保存到数据库

            document.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // PDF文本提取
    @Override
    public String extractPdfText(MultipartFile file) {
        try (PDDocument document = PDDocument.load(file.getInputStream())) {
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }



    /*@Override
    public Object llmAnalyze(String content_pdf) {
        try {
            MessageManager msgManager = new MessageManager(10);

            // 构建系统提示词，根据不同的分析类型提供不同的分析指导
            String systemPrompt = buildPrompt(content_pdf);
            Message systemMessage = Message.builder()
                    .role(Role.SYSTEM.getValue())
                    .content(systemPrompt)
                    .build();

            // 构建用户消息
            Message userMessage = Message.builder()
                    .role(Role.USER.getValue())
                    .content(content_pdf)
                    .build();

            msgManager.add(systemMessage);
            msgManager.add(userMessage);

            QwenParam param = QwenParam.builder()
                    .apiKey(dashscopeApiKey)
                    .model(Generation.Models.QWEN_PLUS)
                    .messages(msgManager.get())
                    .resultFormat("message")
                    .topP(0.8)
                    .temperature(0.01f)
                    .maxTokens(8000)
                    .build();

            // 设置API Key
            //Generation.setApiKey(bailianApiKey);

            // 调用API并处理可能的异常
            GenerationResult result;
            try {
                result = new Generation().call(param);
            } catch (NoApiKeyException e) {
                throw new RuntimeException("API Key未设置或无效", e);
            } catch (InputRequiredException e) {
                throw new RuntimeException("输入参数不完整", e);
            } catch (ApiException e) {
                throw new RuntimeException("API调用失败: " + e.getMessage(), e);
            }

            if (result.getOutput() != null && result.getOutput().getChoices() != null
                    && !result.getOutput().getChoices().isEmpty()) {
                String response = result.getOutput().getChoices().get(0).getMessage().getContent();
                return response;
            } else {
                throw new RuntimeException("AI分析返回结果为空");
            }
            //return objectMapper.readTree(jsonResult);
        } catch (Exception e) {
            //throw new RuntimeException("AI分析失败: " + e.getMessage(), e);
            return "{\"error\": \"无法从AI响应中提取有效的JSON\"}";
        }
    }
*/
    /**
     * LLM分析（百炼��模型调用，示例返回结构）
     * 分析文本并返回结构化数据
     *
     * @param content_pdf 要分析的文本
     * @return 结构化的分析结���
     */
    @Override
    public Object llmAnalyze(String content_pdf) {
        MessageManager msgManager = new MessageManager(10);

        try {
            // 构建系统提示词和用户内容
            String systemPrompt = buildPrompt(content_pdf);
            msgManager.add(Message.builder().role(Role.USER.getValue()).content(systemPrompt).build());
            msgManager.add(Message.builder().role(Role.SYSTEM.getValue()).content("你是一位物流定价专家。请从物流定价文档中提取结构化数据，并按照指定格式输出JSON。你必须严格按照示例格式输出，不要添加任何额外的解释或文本。").build());

            QwenParam param = QwenParam.builder()
                    .apiKey(dashscopeApiKey)
                    .model(Generation.Models.QWEN_PLUS)
                    .messages(msgManager.get())
                    .resultFormat("message")
                    .topP(0.8)
                    .temperature(0.01f)
                    .maxTokens(8000)
                    .build();

            GenerationResult result = new Generation().call(param);

            if (result.getOutput() == null || result.getOutput().getChoices().isEmpty()) {
                throw new RuntimeException("AI分析返回结果为空");
            }

            return result.getOutput().getChoices().get(0).getMessage().getContent();

        } catch (NoApiKeyException e) {
            log.error("[AI分析] API Key未设置或无效: {}", e.getMessage(), e);
            return errorJson("API Key未设置或无效");
        } catch (InputRequiredException e) {
            log.error("[AI分析] 输入参数不完整: {}", e.getMessage(), e);
            return errorJson("输入参数不完整");
        } catch (ApiException e) {
            log.error("[AI分析] 调用API失败，状态码: {}, 信息: {}", e.getStatus(), e.getMessage(), e);
            return errorJson("API调用失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("[AI分析] 未知异常: {}", e.getMessage(), e);
            return errorJson("AI分析失败: " + e.getMessage());
        }
    }

    private String errorJson(String msg) {
        return String.format("{\"error\": \"%s\"}", msg);
    }

    // JSON导入
    @Override
    public int importFromJson(Map<String, Object> json) {
        // 假设json结构为{"orders": [{...}, ...]}
        Object ordersObj = json.get("orders");
        if (!(ordersObj instanceof java.util.List<?> ordersList)) return 0;
        int count = 0;
        for (Object obj : ordersList) {
            if (obj instanceof Map<?, ?> map) {
                Order order = new Order();
                order.setQuotationNo(String.valueOf(map.get("quotationNo")));
                order.setCarrier(String.valueOf(map.get("carrier")));
                order.setStartDate(java.time.LocalDateTime.parse(String.valueOf(map.get("startDate"))));
                order.setEndDate(java.time.LocalDateTime.parse(String.valueOf(map.get("endDate"))));
                order.setRouteCount(Integer.parseInt(String.valueOf(map.get("routeCount"))));
                order.setCurrency(String.valueOf(map.get("currency")));
                order.setStatus(String.valueOf(map.get("status")));
                // TODO: 保存order到数据库
                count++;
                orderMapper.insert(order);
            }
        }
        return count;
    }

    @Override
    public Map<String, Object> saveOrderFromImport(String llmJson, String pdfPath, String carrier, String quotationNo, String status) {
        Map<String, Object> result = new java.util.HashMap<>();
        int totalImported = 0;
        java.util.List<Map<String, Object>> errors = new java.util.ArrayList<>();
        try {
            // 尝试解析llmJson为对象
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            // 兼容llmJson为字符串或对象
            com.fasterxml.jackson.databind.JsonNode root;
            if (llmJson != null && llmJson.trim().startsWith("{")) {
                root = objectMapper.readTree(llmJson);
            } else {
                root = objectMapper.readTree("{}{}");
            }
            // 只存一条，或批量存储
            Order order = new Order();
            order.setLlmJson(llmJson);
            order.setPdfPath(pdfPath);
            order.setCarrier(carrier);
            order.setQuotationNo(quotationNo);
            order.setStatus(status);
            order.setCreateTime(java.time.LocalDateTime.now());
            order.setUpdateTime(java.time.LocalDateTime.now());
            try {
                orderMapper.insert(order);
                totalImported++;
            } catch (Exception e) {
                Map<String, Object> err = new java.util.HashMap<>();
                err.put("row", 1);
                err.put("message", e.getMessage());
                errors.add(err);
            }
        } catch (Exception e) {
            Map<String, Object> err = new java.util.HashMap<>();
            err.put("row", 1);
            err.put("message", "JSON解析失败: " + e.getMessage());
            errors.add(err);
        }
        result.put("totalImported", totalImported);
        result.put("errors", errors);
        return result;
    }

    @Override
    public List<Order> search(int page, int size, String quotationNo, String carrier, String currency, String status) {
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<Order> wrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
        if (quotationNo != null && !quotationNo.isEmpty()) wrapper.eq("quotation_no", quotationNo);
        if (carrier != null && !carrier.isEmpty()) wrapper.eq("carrier", carrier);
        if (currency != null && !currency.isEmpty()) wrapper.eq("currency", currency);
        if (status != null && !status.isEmpty()) wrapper.eq("status", status);
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<Order> pageObj = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page, size);
        orderMapper.selectPage(pageObj, wrapper);
        return pageObj.getRecords();
    }

    @Override
    public int count(String quotationNo, String carrier, String currency, String status) {
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<Order> wrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
        if (quotationNo != null && !quotationNo.isEmpty()) wrapper.eq("quotation_no", quotationNo);
        if (carrier != null && !carrier.isEmpty()) wrapper.eq("carrier", carrier);
        if (currency != null && !currency.isEmpty()) wrapper.eq("currency", currency);
        if (status != null && !status.isEmpty()) wrapper.eq("status", status);
        return orderMapper.selectCount(wrapper).intValue();
    }

    private Double parseFreightFromResponse(String qwenResponse) {
        try {
            return Double.parseDouble(qwenResponse.replaceAll("[^0-9.]", ""));
        } catch (Exception e) {
            return 0.0;
        }
    }

    private String buildPrompt(String pdfText) {
        return "  //重要规则：\n" +
                "  一个 quote 有多个 route；\n" +
                "  一个 routes 只有一个起运港、目的港， 对应一组rates；\n" +
                "  *** 非常重要，文本中的基础价格可能是二维的，请务必拉平，+号代表大于例如+100k代表100kg以上，全部提取出来。一组rates中， 包含一个或多个密度density_factor； 一个密度density_factor会有多个价格等级如 100KG 300KG 每一个价格等级有自己的price  ，必须仔细检查上下文； 每一个rate包含一个密度density_factor、一个价格等级有自己的price 如{ \"min_wt\": 45, \"max_wt\": 100, \"price\": 5.1, \"density_type\": \"Y\", \"density_factor\": 200 },\n" +
                "{ \"min_wt\": 100, \"max_wt\": 200, \"price\": 4.7, \"density_type\": \"Y\", \"density_factor\": 200 }" +
                "以下是从物流定价PDF文档中提取的文本。请分析并以JSON格式提取以下信息，格式必须严格按照示例：\n\n" +
                "{" +
                "  \"currency\": \"CNY\",\n" +
                "  \"quote_date\": \"2023-10-17\",\n" +
                "  \"quote_number\": \"\",\n" +
                "  \"remarks\": [\n" +
                "    {\n" +
                "      \"content\": \"以上价格已包括空运附加费\",\n" +
                "      \"is_important\": true,\n" +
                "      \"type\": \"GENERAL\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"routes\": [\n" +
                "    {\n" +
                "      \"M-rate\": 0,\n" +
                "      \"N-rate\": 0,\n" +
                "      \"carrier_code\": \"LH\",\n" +
                "      \"destination\": \"FRA\",\n" +
                "      \"frequency\": \"DAILY\",\n" +
                "      \"origin\": \"PEK\",\n" +
                "      \"rates\": [\n" +
                "        {\n" +
                "          \"density_factor\": 80,\n" +
                "          \"density_type\": \"Y\",\n" +
                "          \"max_wt\": 300,\n" +
                "          \"min_wt\": 100,\n" +
                "          \"price\": 26\n" +
                "        }\n" +
                "      ],\n" +
                "      \"surcharges\": [  {\n" +
                "            \"surcharges_name\":\"\",\n" +
                "            \"surcharges_cost\":\"\",\n" +
                "        }],\n" +
                "      \"transfers\": [        {\n" +
                "          \"cost_currency\": \"CNY\",\n" +
                "          \"destinations\": [\n" +
                "            {\n" +
                "              \"to_port\": \"AMS\"\n" +
                "            },\n" +
                "            {\n" +
                "              \"to_port\": \"BRU\"\n" +
                "            }\n" +
                "          ],\n" +
                "          \"from_port\": \"PEK\",\n" +
                "          \"transfer_cost\": 1\n" +
                "        }\n" +
                "]\n" +
                "    }]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"source_file\": \"\",\n" +
                "  \"status\": \"ACTIVE\",\n" +
                "  \"valid_from\": \"2024-05-29\",\n" +
                "  \"valid_to\": \"2024-08-27\",\n" +
                "  \"vendor_code\": \"BJZK\"\n" +
                "}\n\n" +
                "请确保输出的JSON格式与示例完全一致，包括字段名称和数据类型。如果文字描述中包含其他报价信息，放在附加费surcharges里,这些附加费就不用放在备注里了，" +
                "如果某些信息在文档中不存在，请使用合理的默认值或空值。\n\n" +
                "请只输出JSON，不要添加任何额外的解释或文本。\n\n" +
                "文档文本：\n" + pdfText;
    }

    @Override
    public List<Map<String, Object>> getCommonRoutes() {
        // 示例实现，实际应从数据库或缓存获取
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getHistory(String type) {
        // 示例实现，实际应从数据库获取
        return new ArrayList<>();
    }

    @Override
    public void saveHistory(Map<String, Object> body) {
        // 示例实现，实际应保存到数据库
    }

    @Override
    public void saveTemplate(Map<String, Object> body) {
        // 示例实现，实际应保存到数据库
    }
}

